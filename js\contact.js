// Contact Page JavaScript
// Enhanced form validation and user interactions

document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    const formGroups = document.querySelectorAll('.form-group');
    const inputs = document.querySelectorAll('input, select, textarea');
    
    // Form validation patterns
    const validationPatterns = {
        name: /^[a-zA-Z\s]{2,50}$/,
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        phone: /^[\+]?[0-9\-\s\(\)]{10,15}$/
    };
    
    // Error messages
    const errorMessages = {
        name: 'Please enter a valid name (2-50 characters, letters only)',
        email: 'Please enter a valid email address',
        phone: 'Please enter a valid phone number',
        legal_service: 'Please select a legal service',
        urgency: 'Please select urgency level',
        contact_method: 'Please select preferred contact method',
        description: 'Please describe your legal matter (minimum 10 characters)',
        privacy: 'Please accept the privacy policy to continue'
    };
    
    // Add floating label animation
    inputs.forEach(input => {
        // Handle input focus and blur for floating labels
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
            validateField(this);
        });
        
        // Real-time validation
        input.addEventListener('input', function() {
            clearError(this);
            if (this.value) {
                validateField(this);
            }
        });
        
        // Initialize floating labels for pre-filled inputs
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });

    // Character counter for textarea
    const descriptionTextarea = document.getElementById('description');
    const charCounter = document.getElementById('char-count');

    if (descriptionTextarea && charCounter) {
        descriptionTextarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            const maxLength = this.getAttribute('maxlength') || 1000;

            charCounter.textContent = currentLength;

            // Change color based on character count
            const counterContainer = charCounter.parentElement;
            if (currentLength > maxLength * 0.9) {
                counterContainer.style.color = '#ef4444';
            } else if (currentLength > maxLength * 0.7) {
                counterContainer.style.color = '#f59e0b';
            } else {
                counterContainer.style.color = '#6b7280';
            }
        });
    }
    
    // Validate individual field
    function validateField(field) {
        const fieldName = field.name;
        const fieldValue = field.value.trim();
        const fieldGroup = field.parentElement;
        
        // Clear previous errors
        clearError(field);
        
        // Check if field is required and empty
        if (field.required && !fieldValue) {
            showError(field, `${field.labels[0].textContent.replace('*', '').trim()} is required`);
            return false;
        }
        
        // Validate specific fields
        switch (fieldName) {
            case 'name':
                if (fieldValue && !validationPatterns.name.test(fieldValue)) {
                    showError(field, errorMessages.name);
                    return false;
                }
                break;

            case 'email':
                if (fieldValue && !validationPatterns.email.test(fieldValue)) {
                    showError(field, errorMessages.email);
                    return false;
                }
                break;

            case 'phone':
                if (fieldValue && !validationPatterns.phone.test(fieldValue)) {
                    showError(field, errorMessages.phone);
                    return false;
                }
                break;

            case 'legal_service':
                if (field.required && !fieldValue) {
                    showError(field, errorMessages.legal_service);
                    return false;
                }
                break;

            case 'urgency':
                if (field.required && !fieldValue) {
                    showError(field, errorMessages.urgency);
                    return false;
                }
                break;

            case 'contact_method':
                if (field.required && !fieldValue) {
                    showError(field, errorMessages.contact_method);
                    return false;
                }
                break;

            case 'description':
                if (fieldValue && fieldValue.length < 10) {
                    showError(field, errorMessages.description);
                    return false;
                }
                break;

            case 'privacy':
                if (field.type === 'checkbox' && !field.checked) {
                    showError(field, errorMessages.privacy);
                    return false;
                }
                break;
        }
        
        // Show success state
        fieldGroup.classList.add('success');
        return true;
    }
    
    // Show error message
    function showError(field, message) {
        const fieldGroup = field.parentElement;
        const errorElement = fieldGroup.querySelector('.error-message');
        
        fieldGroup.classList.add('error');
        fieldGroup.classList.remove('success');
        
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }
    
    // Clear error message
    function clearError(field) {
        const fieldGroup = field.parentElement;
        const errorElement = fieldGroup.querySelector('.error-message');
        
        fieldGroup.classList.remove('error');
        
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
    
    // Form submission
    contactForm.addEventListener('submit', function(e) {
        let isValid = true;

        // Validate all fields
        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault(); // Only prevent submission if validation fails

            // Scroll to first error
            const firstError = document.querySelector('.form-group.error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            // Show loading state before form submits
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Sending...</span>';
            submitBtn.disabled = true;

            // Form will submit naturally to FormSubmit.co
            // FormSubmit.co will handle the redirect
        }
    });
    
    // Submit form (simulate submission)
    function submitForm(formData) {
        const submitBtn = contactForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Sending...</span>';
        submitBtn.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            // Show success message
            showSuccessMessage();
            
            // Reset form
            contactForm.reset();
            formGroups.forEach(group => {
                group.classList.remove('focused', 'success', 'error');
            });
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            
        }, 2000);
    }
    
    // Show success message
    function showSuccessMessage() {
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message';
        successMessage.innerHTML = `
            <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 2rem; border-radius: 15px; text-align: center; margin: 2rem 0; box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);">
                <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                <h3 style="margin-bottom: 1rem; color: white;">Message Sent Successfully!</h3>
                <p style="margin: 0; opacity: 0.9;">Thank you for contacting us. We'll get back to you within 24 hours.</p>
            </div>
        `;
        
        contactForm.parentNode.insertBefore(successMessage, contactForm);
        
        // Scroll to success message
        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Remove success message after 5 seconds
        setTimeout(() => {
            successMessage.remove();
        }, 5000);
    }
    
    // Add hover effects to contact items
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add click-to-copy functionality for contact details
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    
    [...phoneLinks, ...emailLinks].forEach(link => {
        link.addEventListener('click', function(e) {
            const text = this.textContent;
            
            // Copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopyNotification(this, 'Copied to clipboard!');
                });
            }
        });
    });
    
    // Show copy notification
    function showCopyNotification(element, message) {
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: absolute;
            background: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
            z-index: 1000;
            pointer-events: none;
            transform: translateY(-100%);
            opacity: 0;
            transition: all 0.3s ease;
        `;
        
        element.style.position = 'relative';
        element.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(-120%)';
        }, 10);
        
        // Hide notification
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    // Check for success parameter in URL (from FormSubmit.co redirect)
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('success') === 'true') {
        // Show success message
        showSuccessMessage();

        // Clean up URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }
});
